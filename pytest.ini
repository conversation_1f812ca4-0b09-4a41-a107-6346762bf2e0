[pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts =
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    --cov=.
    --cov-report=term-missing
    --cov-fail-under=15
    --ignore-glob=**/mock_*
    --ignore-glob=**/mcp_*
    --ignore-glob=**/crewai*
    --ignore=artist_experiments
    --ignore=test_mem0.py
    --ignore=test_mem0_integration.py
    --ignore=test_crewai_simple.py
    --ignore=test_mock_crewai_fix.py
    --ignore=test_python.py
    --ignore=test_security_reports.py
    --ignore=test_simple.py
    --ignore=test_workflow_fixes.py
    --ignore=test_bandit_config.py
markers =
    slow: marks tests as slow
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    webhook: marks tests as webhook tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning
    ignore::pytest_asyncio.plugin.PytestDeprecationWarning
    ignore::pytest.PytestDeprecationWarning
asyncio_default_fixture_loop_scope = function
asyncio_mode = auto
asyncio_default_test_loop_scope = function
